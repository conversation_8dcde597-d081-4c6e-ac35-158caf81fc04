.error-report-detail-modal {
  .ant-modal-content {
    border-radius: 8px;
    box-shadow: var(--shadow-level-2);
  }

  .ant-modal-header {
    border-radius: 8px 8px 0 0;
    background: var(--primary-colours-blue-navy);
    padding: 20px 24px;

    .ant-modal-title {
      color: var(--typo-colours-support-white);
      font-weight: 600;
      font-size: 18px;
    }
  }

  .ant-modal-close {
    .ant-modal-close-x {
      color: var(--typo-colours-support-white);
      font-size: 18px;

      &:hover {
        color: var(--typo-colours-support-white);
        opacity: 0.8;
      }
    }
  }

  .ant-modal-body {
    padding: 24px;
    max-height: 75vh;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

.error-report-detail-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  font-family: 'Segoe UI', serif;

  // Simple header
  .error-report-header {
    margin-bottom: 8px;

    .error-report-title {
      margin-bottom: 0;
      color: var(--typo-colours-primary-black);
      font-size: 20px;
      font-weight: 600;
    }
  }

  // Common card styles - simplified
  .info-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 20px;
    }

    .ant-card-head {
      background-color: var(--background-light-background-1);
      border-bottom: 1px solid var(--lighttheme-content-background-stroke);
      border-radius: 8px 8px 0 0;

      .ant-card-head-title {
        padding: 14px 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--typo-colours-primary-black);
      }
    }
  }

  // Info items styling
  .info-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-bottom: 16px;

    .info-label {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey);
    }

    .info-value {
      font-size: 15px;
      color: var(--typo-colours-primary-black);
      font-weight: 500;
    }

    .info-link {
      color: var(--primary-colours-blue);
      text-decoration: none;
      word-break: break-all;
      font-size: 15px;
      transition: all var(--transition-timing);

      &:hover {
        color: var(--primary-colours-blue-navy);
        text-decoration: underline;
      }
    }
  }

  // Description text styling
  .description-text {
    margin-bottom: 0;
    line-height: 1.7;
    color: var(--typo-colours-primary-black);
    white-space: pre-wrap;
    word-break: break-word;
    font-size: 15px;
    background-color: var(--background-light-background-2);
    border-radius: 6px;
    padding: 16px;
    border: 1px solid var(--lighttheme-content-background-stroke);
  }

  // Media content styles
  .media-content {
    .images-section {
      margin-bottom: 32px;

      .media-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;

        .media-icon {
          color: var(--primary-colours-blue);
          font-size: 16px;
        }

        .media-title {
          margin-bottom: 0;
          color: var(--typo-colours-primary-black);
          font-size: 16px;
          font-weight: 600;
        }
      }

      .images-gallery {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 16px;

        .image-wrapper {
          position: relative;
          border-radius: 8px;
          overflow: hidden;
          transition: all var(--transition-timing);

          &:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-level-2);

            .image-overlay {
              opacity: 1;
            }
          }

          .error-image {
            border: 2px solid var(--background-light-background-1);
            transition: all var(--transition-timing);
            cursor: pointer;

            &:hover {
              border-color: var(--primary-colours-blue);
            }

            .image-preview-mask {
              display: flex;
              align-items: center;
              justify-content: center;
              background: rgba(0, 0, 0, 0.6);
              border-radius: 4px;
              padding: 8px;
              transition: all var(--transition-timing);

              &:hover {
                background: rgba(0, 0, 0, 0.8);
              }
            }
          }

          .image-overlay {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity var(--transition-timing);

            .image-number {
              font-size: 12px;
              font-weight: 600;
              color: white;
            }
          }

          .image-error {
            width: 140px;
            height: 140px;
            border: 2px dashed var(--lighttheme-content-background-stroke);
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: var(--background-light-background-2);
            text-align: center;
            padding: 16px;
          }
        }
      }
    }

    .video-section {
      margin-bottom: 24px;

      .media-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;

        .media-icon {
          color: var(--primary-colours-blue);
          font-size: 16px;
        }

        .media-title {
          margin-bottom: 0;
          color: var(--typo-colours-primary-black);
          font-size: 16px;
          font-weight: 600;
        }
      }

      .video-player {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: var(--shadow-level-1);
        background: #000;
        position: relative;

        .error-video {
          border-radius: 8px;
          border: 1px solid var(--lighttheme-content-background-stroke);
          width: 100%;
          background: #000;

          &:focus {
            outline: 2px solid var(--primary-colours-blue);
            outline-offset: 2px;
          }
        }

        .video-error {
          width: 100%;
          height: 320px;
          border: 2px dashed var(--lighttheme-content-background-stroke);
          border-radius: 8px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background-color: var(--background-light-background-2);
          text-align: center;
        }

        // Custom video controls styling
        .error-video::-webkit-media-controls-panel {
          background-color: rgba(0, 0, 0, 0.8);
        }

        .error-video::-webkit-media-controls-play-button,
        .error-video::-webkit-media-controls-volume-slider,
        .error-video::-webkit-media-controls-timeline {
          filter: brightness(1.2);
        }
      }
    }
  }

  .no-media {
    text-align: center;
    padding: 60px 20px;
    background-color: var(--background-light-background-2);
    border-radius: 8px;
    border: 2px dashed var(--lighttheme-content-background-stroke);

    .no-media-icon {
      font-size: 48px;
      color: var(--typo-colours-support-blue-light);
      margin-bottom: 16px;
    }

    .no-media-text {
      color: var(--typo-colours-support-blue-light);
      font-size: 16px;
      font-style: italic;
    }
  }
}

// Admin actions card styles
.admin-actions-card {
  .ant-card-head {
    background-color: var(--primary-colours-blue-light-1);
  }


}

// Image preview customization
.ant-image-preview-wrap {
  .ant-image-preview-img {
    max-height: 85vh;
    object-fit: contain;
  }

  .ant-image-preview-operations {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 6px;
  }
}

// Animation for loading states
.ant-spin-spinning {
  .error-report-detail-container {
    opacity: 0.7;
    pointer-events: none;
  }
}

// Responsive design
@media (max-width: 1024px) {
  .error-report-detail-modal {
    .ant-modal {
      width: 95% !important;
      margin: 10px;
    }

    .ant-modal-content {
      .ant-modal-body {
        padding: 16px;
      }
    }
  }

  .error-report-detail-container {
    gap: 16px;

    .info-card {
      .ant-row {
        .ant-col {
          margin-bottom: 16px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .error-report-detail-container {
    .media-content {
      .images-section {
        .images-gallery {
          grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
          gap: 12px;

          .image-wrapper {
            .error-image {
              width: 120px !important;
              height: 120px !important;
            }
          }
        }
      }

      .video-section {
        .video-player {
          .error-video {
            height: 200px;
          }
        }
      }
    }
  }
}

// Hover effects and transitions
.error-report-detail-container {
  .ant-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      transform: translateY(-1px);
    }
  }

  .ant-divider {
    margin: 20px 0;
    border-color: var(--lighttheme-content-background-stroke);
  }

  // Custom tag styles
  .ant-tag {
    border-radius: 4px;
    font-weight: 500;
    padding: 4px 8px;
    border: none;
  }

  // Link styles
  a {
    transition: all var(--transition-timing);

    &:hover {
      text-decoration: none;
    }
  }
}
