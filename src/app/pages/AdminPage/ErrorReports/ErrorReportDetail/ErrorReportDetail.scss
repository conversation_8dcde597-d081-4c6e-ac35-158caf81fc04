.error-report-detail-modal {
  .ant-modal-content {
    border-radius: 8px;
    box-shadow: var(--shadow-level-2);
  }

  .ant-modal-header {
    border-radius: 8px 8px 0 0;
    background: var(--primary-colours-blue-navy);
    padding: 20px 24px;

    .ant-modal-title {
      color: var(--typo-colours-support-white);
      font-weight: 600;
      font-size: 18px;

      .modal-title-wrapper {
        display: flex;
        align-items: center;
        gap: 12px;

        .modal-title {
          font-size: 18px;
          font-weight: 600;
        }

        .impact-tag {
          font-size: 12px;
          font-weight: 500;
          border-radius: 4px;
        }
      }
    }
  }

  .ant-modal-close {
    .ant-modal-close-x {
      color: var(--typo-colours-support-white);
      font-size: 18px;

      &:hover {
        color: var(--typo-colours-support-white);
        opacity: 0.8;
      }
    }
  }

  .ant-modal-body {
    padding: 24px;
    max-height: 75vh;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

.error-report-detail-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: 'Segoe UI', serif;

  // Common card styles following admin pattern
  .error-report-info-card,
  .user-info-card,
  .error-details-card,
  .media-card,
  .admin-actions-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }

    .ant-card-head {
      background-color: var(--background-light-background-1);
      border-bottom: 1px solid var(--lighttheme-content-background-stroke);
      border-radius: 8px 8px 0 0;

      .ant-card-head-title {
        padding: 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--typo-colours-primary-black);

        .card-title {
          display: flex;
          align-items: center;
          gap: 8px;

          .card-icon {
            color: var(--primary-colours-blue);
            font-size: 16px;
          }
        }
      }
    }
  }

  // Header card specific styles
  .error-report-info-card {
    .error-report-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .header-left {
        .error-id {
          margin-bottom: 8px;
          color: var(--typo-colours-primary-black);
          font-size: 20px;
          font-weight: 700;
        }

        .status-info {
          .notification-tag {
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
          }
        }
      }

      .header-right {
        .timestamp-info {
          display: flex;
          flex-direction: column;
          gap: 8px;
          align-items: flex-end;

          .timestamp-item {
            display: flex;
            align-items: center;
            gap: 6px;

            .timestamp-icon {
              color: var(--primary-colours-blue);
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  // User info card styles
  .user-info-card {
    .user-detail {
      display: flex;
      flex-direction: column;
      gap: 4px;
      margin-bottom: 16px;

      .user-label {
        font-size: 14px;
        color: var(--typo-colours-secondary-grey);
      }

      .user-value {
        font-size: 16px;
        color: var(--typo-colours-primary-black);
        font-weight: 500;
      }
    }
  }

  // Error details card styles
  .error-details-card {
    .error-url-section {
      margin-bottom: 16px;

      .detail-label {
        display: block;
        margin-bottom: 8px;
        font-size: 14px;
        color: var(--typo-colours-secondary-grey);
      }

      .url-wrapper {
        .error-url-link {
          color: var(--primary-colours-blue);
          text-decoration: none;
          word-break: break-all;
          font-size: 14px;
          transition: all var(--transition-timing);

          &:hover {
            color: var(--primary-colours-blue-navy);
            text-decoration: underline;
          }
        }
      }
    }

    .description-section {
      .detail-label {
        display: block;
        margin-bottom: 12px;
        font-size: 14px;
        color: var(--typo-colours-secondary-grey);
      }

      .description-content {
        background-color: var(--background-light-background-2);
        border-radius: 6px;
        padding: 16px;
        border: 1px solid var(--lighttheme-content-background-stroke);

        .description-text {
          margin-bottom: 0;
          line-height: 1.6;
          color: var(--typo-colours-primary-black);
          white-space: pre-wrap;
          word-break: break-word;
          font-size: 15px;
        }
      }
    }
  }

  // Media card styles
  .media-card {
    .media-content {
      .images-section {
        margin-bottom: 32px;

        .media-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 16px;

          .media-icon {
            color: var(--primary-colours-blue);
            font-size: 16px;
          }

          .media-title {
            margin-bottom: 0;
            color: var(--typo-colours-primary-black);
            font-size: 16px;
            font-weight: 600;
          }
        }

        .images-gallery {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
          gap: 16px;

          .image-wrapper {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            transition: all var(--transition-timing);

            &:hover {
              transform: translateY(-2px);
              box-shadow: var(--shadow-level-2);

              .image-overlay {
                opacity: 1;
              }
            }

            .error-image {
              border: 2px solid var(--background-light-background-1);
              transition: all var(--transition-timing);
              cursor: pointer;

              &:hover {
                border-color: var(--primary-colours-blue);
              }
            }

            .image-overlay {
              position: absolute;
              top: 8px;
              right: 8px;
              background: rgba(0, 0, 0, 0.7);
              color: white;
              border-radius: 50%;
              width: 24px;
              height: 24px;
              display: flex;
              align-items: center;
              justify-content: center;
              opacity: 0;
              transition: opacity var(--transition-timing);

              .image-number {
                font-size: 12px;
                font-weight: 600;
                color: white;
              }
            }
          }
        }
      }

      .video-section {
        margin-bottom: 24px;

        .media-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 16px;

          .media-icon {
            color: var(--primary-colours-blue);
            font-size: 16px;
          }

          .media-title {
            margin-bottom: 0;
            color: var(--typo-colours-primary-black);
            font-size: 16px;
            font-weight: 600;
          }
        }

        .video-player {
          border-radius: 8px;
          overflow: hidden;
          box-shadow: var(--shadow-level-1);

          .error-video {
            border-radius: 8px;
            border: 1px solid var(--lighttheme-content-background-stroke);
            width: 100%;
          }
        }
      }

      .no-media {
        text-align: center;
        padding: 60px 20px;
        background-color: var(--background-light-background-2);
        border-radius: 8px;
        border: 2px dashed var(--lighttheme-content-background-stroke);

        .no-media-icon {
          font-size: 48px;
          color: var(--typo-colours-support-blue-light);
          margin-bottom: 16px;
        }

        .no-media-text {
          color: var(--typo-colours-support-blue-light);
          font-size: 16px;
          font-style: italic;
        }
      }
    }
  }

  // Admin actions card styles
  .admin-actions-card {
    .ant-card-head {
      background-color: var(--primary-colours-blue-light-1);
    }

    .notification-control {
      margin-bottom: 24px;

      .control-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;

        .control-label {
          font-size: 16px;
          color: var(--typo-colours-primary-black);
        }

        .notification-switch {
          &.ant-switch-checked {
            background-color: var(--support-colours-green);
          }

          &:hover:not(.ant-switch-disabled) {
            background-color: var(--support-colours-green);
            opacity: 0.8;
          }
        }
      }

      .control-description {
        font-size: 14px;
        color: var(--typo-colours-secondary-grey);
        line-height: 1.5;
      }
    }

    .admin-notes-section {
      .notes-header {
        margin-bottom: 16px;

        .notes-label {
          display: block;
          font-size: 16px;
          color: var(--typo-colours-primary-black);
          margin-bottom: 4px;
        }

        .notes-description {
          font-size: 14px;
          color: var(--typo-colours-secondary-grey);
          line-height: 1.5;
        }
      }

      .notes-textarea {
        border-radius: 6px;
        font-size: 14px;
        line-height: 1.6;
        resize: vertical;
        min-height: 100px;

        &:focus {
          border-color: var(--primary-colours-blue);
          box-shadow: 0 0 0 2px var(--primary-colours-blue-light-2);
        }

        &::placeholder {
          color: var(--typo-colours-support-blue-light);
          font-style: italic;
        }
      }

      .notes-actions {
        margin-top: 16px;
        text-align: right;

        .save-notes-btn {
          min-width: 120px;
          height: 40px;
          border-radius: 6px;
          font-weight: 500;
          transition: all var(--transition-timing);

          &:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: var(--shadow-level-2);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }
    }
  }
}
// Image preview customization
.ant-image-preview-wrap {
  .ant-image-preview-img {
    max-height: 85vh;
    object-fit: contain;
  }

  .ant-image-preview-operations {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 6px;
  }
}

// Animation for loading states
.ant-spin-spinning {
  .error-report-detail-container {
    opacity: 0.7;
    pointer-events: none;
  }
}

// Responsive design
@media (max-width: 1024px) {
  .error-report-detail-modal {
    .ant-modal {
      width: 95% !important;
      margin: 10px;
    }

    .ant-modal-content {
      .ant-modal-body {
        padding: 16px;
      }
    }
  }

  .error-report-detail-container {
    gap: 16px;

    .error-report-info-card {
      .error-report-header {
        flex-direction: column;
        gap: 16px;

        .header-right {
          .timestamp-info {
            align-items: flex-start;
          }
        }
      }
    }

    .user-info-card {
      .ant-row {
        .ant-col {
          margin-bottom: 16px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .error-report-detail-container {
    .media-card {
      .media-content {
        .images-section {
          .images-gallery {
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 12px;

            .image-wrapper {
              .error-image {
                width: 120px !important;
                height: 120px !important;
              }
            }
          }
        }

        .video-section {
          .video-player {
            .error-video {
              height: 200px;
            }
          }
        }
      }
    }

    .admin-actions-card {
      .notification-control {
        .control-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;
        }
      }

      .admin-notes-section {
        .notes-actions {
          text-align: left;

          .save-notes-btn {
            width: 100%;
          }
        }
      }
    }
  }
}

// Hover effects and transitions
.error-report-detail-container {
  .ant-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      transform: translateY(-1px);
    }
  }

  .ant-divider {
    margin: 20px 0;
    border-color: var(--lighttheme-content-background-stroke);
  }

  // Custom tag styles
  .ant-tag {
    border-radius: 4px;
    font-weight: 500;
    padding: 4px 8px;
    border: none;
  }

  // Link styles
  a {
    transition: all var(--transition-timing);

    &:hover {
      text-decoration: none;
    }
  }
}
