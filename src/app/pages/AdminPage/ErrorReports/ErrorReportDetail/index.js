import React, { useEffect, useState } from "react";
import { Card, Col, Row, Tag, Image, Button, Space, Descriptions, Typography, Switch, Input, Divider } from "antd";
import { useTranslation } from "react-i18next";
import { CheckOutlined, CloseOutlined, PlayCircleOutlined, UserOutlined, LinkOutlined, CalendarOutlined, FileImageOutlined, VideoCameraOutlined } from "@ant-design/icons";

import AntModal from "@src/app/component/AntModal";
import Loading from "@src/app/component/Loading";
import AntButton from "@src/app/component/AntButton";

import { formatDate } from "@src/common/functionCommons";
import { BUTTON } from "@constant";
import { getErrorReportDetail, updateErrorReport } from "@services/ErrorReport";
import { toast } from "@src/app/component/ToastProvider";

import "./ErrorReportDetail.scss";

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

function ErrorReportDetail({ open, errorReport, onClose, onUpdate }) {
  const { t } = useTranslation();
  const [detailData, setDetailData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [adminNotes, setAdminNotes] = useState("");
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    if (open && errorReport?._id) {
      fetchErrorReportDetail();
    }
  }, [open, errorReport]);

  const fetchErrorReportDetail = async () => {
    setIsLoading(true);
    try {
      const response = await getErrorReportDetail(errorReport._id);
      if (response) {
        setDetailData(response);
        setAdminNotes(response.adminNotes || "");
      }
    } catch (error) {
      toast.error(t("FETCH_ERROR_REPORT_DETAIL_ERROR"));
    }
    setIsLoading(false);
  };

  const handleUpdateStatus = async (field, value) => {
    setIsUpdating(true);
    try {
      const updateData = {
        _id: detailData._id,
        [field]: value
      };

      const response = await updateErrorReport(updateData);
      if (response) {
        setDetailData({ ...detailData, [field]: value });
        onUpdate && onUpdate({ ...detailData, [field]: value });
        toast.success(t("ERROR_REPORT_UPDATED_SUCCESS"));
      }
    } catch (error) {
      toast.error(t("UPDATE_ERROR_REPORT_ERROR"));
    }
    setIsUpdating(false);
  };

  const handleSaveNotes = async () => {
    setIsUpdating(true);
    try {
      const response = await updateErrorReport({
        _id: detailData._id,
        adminNotes
      });

      if (response) {
        setDetailData({ ...detailData, adminNotes });
        toast.success(t("ADMIN_NOTES_SAVED_SUCCESS"));
      }
    } catch (error) {
      toast.error(t("SAVE_ADMIN_NOTES_ERROR"));
    }
    setIsUpdating(false);
  };

  const getImpactLevelColor = (level) => {
    switch (level) {
      case "high": return "red";
      case "medium": return "orange";
      case "low": return "green";
      default: return "default";
    }
  };

  const renderMediaContent = () => {
    if (!detailData) return null;

    const { imageIds = [], videoId } = detailData;

    return (
      <div className="media-content">
        {imageIds.length > 0 && (
          <div className="images-section">
            <div className="media-header">
              <FileImageOutlined className="media-icon" />
              <Title level={5} className="media-title">
                {t("ATTACHED_IMAGES")} ({imageIds.length})
              </Title>
            </div>
            <div className="images-gallery">
              <Image.PreviewGroup>
                {imageIds.map((image, index) => (
                  <div key={image._id || index} className="image-wrapper">
                    <Image
                      width={140}
                      height={140}
                      src={image.url || image.path}
                      alt={`Error image ${index + 1}`}
                      className="error-image"
                      style={{ objectFit: 'cover', borderRadius: '8px' }}
                    />
                    <div className="image-overlay">
                      <Text className="image-number">{index + 1}</Text>
                    </div>
                  </div>
                ))}
              </Image.PreviewGroup>
            </div>
          </div>
        )}

        {videoId && (
          <div className="video-section">
            <div className="media-header">
              <VideoCameraOutlined className="media-icon" />
              <Title level={5} className="media-title">{t("ATTACHED_VIDEO")}</Title>
            </div>
            <div className="video-player">
              <video
                controls
                width="100%"
                height="320"
                src={videoId.url || videoId.path}
                className="error-video"
              >
                {t("VIDEO_NOT_SUPPORTED")}
              </video>
            </div>
          </div>
        )}

        {imageIds.length === 0 && !videoId && (
          <div className="no-media">
            <div className="no-media-icon">
              <FileImageOutlined />
            </div>
            <Text type="secondary" className="no-media-text">
              {t("NO_MEDIA_ATTACHED")}
            </Text>
          </div>
        )}
      </div>
    );
  };

  if (!detailData) {
    return (
      <AntModal
        open={open}
        onCancel={onClose}
        title={t("ERROR_REPORT_DETAILS")}
        width={1000}
        footer={null}
      >
        <Loading active={isLoading} transparent>
          <div style={{ height: 200 }} />
        </Loading>
      </AntModal>
    );
  }

  return (
    <AntModal
      open={open}
      onCancel={onClose}
      title={
        <div className="modal-title-wrapper">
          <span className="modal-title">{t("ERROR_REPORT_DETAILS")}</span>
          {detailData && (
            <Tag color={getImpactLevelColor(detailData.impactLevel)} className="impact-tag">
              {t(`IMPACT_${detailData.impactLevel?.toUpperCase()}`)}
            </Tag>
          )}
        </div>
      }
      width={1200}
      footer={null}
      className="error-report-detail-modal"
    >
      <Loading active={isLoading || isUpdating} transparent>
        <div className="error-report-detail-container">
          {/* Header Information Card */}
          <Card className="error-report-info-card">
            <div className="error-report-header">
              <div className="header-left">
                <Title level={4} className="error-id">
                  {t("ERROR_REPORT")} #{detailData._id?.slice(-8)}
                </Title>
                <div className="status-info">
                  <Tag color={detailData.notified ? 'green' : 'orange'} className="notification-tag">
                    {detailData.notified ? t("NOTIFIED") : t("NOT_NOTIFIED")}
                  </Tag>
                </div>
              </div>
              <div className="header-right">
                <div className="timestamp-info">
                  <div className="timestamp-item">
                    <CalendarOutlined className="timestamp-icon" />
                    <Text type="secondary">{t("CREATED")}: {formatDate(detailData.createdAt)}</Text>
                  </div>
                  <div className="timestamp-item">
                    <CalendarOutlined className="timestamp-icon" />
                    <Text type="secondary">{t("UPDATED")}: {formatDate(detailData.updatedAt)}</Text>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* User Information */}
          <Card title={
            <div className="card-title">
              <UserOutlined className="card-icon" />
              <span>{t("USER_INFORMATION")}</span>
            </div>
          } className="user-info-card">
            <Row gutter={24}>
              <Col span={12}>
                <div className="user-detail">
                  <Text strong className="user-label">{t("EMAIL")}:</Text>
                  <Text className="user-value">{detailData.userId?.email}</Text>
                </div>
              </Col>
              <Col span={12}>
                <div className="user-detail">
                  <Text strong className="user-label">{t("FULL_NAME")}:</Text>
                  <Text className="user-value">{detailData.userId?.fullName || t("NOT_PROVIDED")}</Text>
                </div>
              </Col>
            </Row>
          </Card>

          {/* Error Details */}
          <Card title={
            <div className="card-title">
              <LinkOutlined className="card-icon" />
              <span>{t("ERROR_DETAILS")}</span>
            </div>
          } className="error-details-card">
            <div className="error-url-section">
              <Text strong className="detail-label">{t("ERROR_URL")}:</Text>
              <div className="url-wrapper">
                <a
                  href={detailData.errorUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="error-url-link"
                >
                  {detailData.errorUrl}
                </a>
              </div>
            </div>

            <Divider />

            <div className="description-section">
              <Text strong className="detail-label">{t("DESCRIPTION")}:</Text>
              <div className="description-content">
                <Paragraph className="description-text">
                  {detailData.description}
                </Paragraph>
              </div>
            </div>
          </Card>

          {/* Media Content */}
          <Card title={
            <div className="card-title">
              <FileImageOutlined className="card-icon" />
              <span>{t("MEDIA_CONTENT")}</span>
            </div>
          } className="media-card">
            {renderMediaContent()}
          </Card>

          {/* Admin Actions */}
          <Card title={
            <div className="card-title">
              <CheckOutlined className="card-icon" />
              <span>{t("ADMIN_ACTIONS")}</span>
            </div>
          } className="admin-actions-card">
            <div className="notification-control">
              <div className="control-header">
                <Text strong className="control-label">{t("NOTIFICATION_STATUS")}</Text>
                <Switch
                  checked={detailData.notified}
                  onChange={(checked) => handleUpdateStatus('notified', checked)}
                  checkedChildren={<CheckOutlined />}
                  unCheckedChildren={<CloseOutlined />}
                  className="notification-switch"
                />
              </div>
              <Text type="secondary" className="control-description">
                {detailData.notified ? t("USER_HAS_BEEN_NOTIFIED") : t("USER_NOT_NOTIFIED_YET")}
              </Text>
            </div>

            <Divider />

            <div className="admin-notes-section">
              <div className="notes-header">
                <Text strong className="notes-label">{t("ADMIN_NOTES")}</Text>
                <Text type="secondary" className="notes-description">
                  {t("ADD_INTERNAL_NOTES_FOR_TRACKING")}
                </Text>
              </div>
              <TextArea
                value={adminNotes}
                onChange={(e) => setAdminNotes(e.target.value)}
                placeholder={t("ADD_ADMIN_NOTES_PLACEHOLDER")}
                rows={4}
                className="notes-textarea"
              />
              <div className="notes-actions">
                <AntButton
                  type={BUTTON.DEEP_NAVY}
                  onClick={handleSaveNotes}
                  disabled={adminNotes === (detailData.adminNotes || "")}
                  className="save-notes-btn"
                >
                  {t("SAVE_NOTES")}
                </AntButton>
              </div>
            </div>
          </Card>
        </div>
      </Loading>
    </AntModal>
  );
}

export default ErrorReportDetail;
