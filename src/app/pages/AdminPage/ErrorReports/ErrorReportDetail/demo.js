import React, { useState } from "react";
import { But<PERSON> } from "antd";
import ErrorReportDetail from "./index";

// Demo data for testing simplified view
const mockErrorReport = {
  _id: "67890abcdef12345",
  userId: {
    _id: "user123",
    email: "<EMAIL>",
    fullName: "John Doe"
  },
  description: "The application crashes when trying to upload a large file. This happens consistently with files over 10MB. The error occurs after clicking the upload button and waiting for about 30 seconds. The page becomes unresponsive and shows a white screen.\n\nSteps to reproduce:\n1. Navigate to upload page\n2. Select a file larger than 10MB\n3. Click upload button\n4. Wait for 30 seconds\n5. Page becomes unresponsive",
  errorUrl: "https://app.example.com/upload/documents",
  imageIds: [
    {
      _id: "img1",
      imageFileId: "file123",
      name: "error-screenshot-1.png"
    },
    {
      _id: "img2",
      imageFileId: "file124",
      name: "error-screenshot-2.png"
    }
  ],
  videoId: {
    _id: "video1",
    name: "error-recording.mp4"
  },
  createdAt: "2024-01-15T10:30:00Z"
};

function ErrorReportDetailDemo() {
  const [isOpen, setIsOpen] = useState(false);

  const handleOpen = () => setIsOpen(true);
  const handleClose = () => setIsOpen(false);

  return (
    <div style={{ padding: "20px" }}>
      <h2>ErrorReportDetail Component Demo (Clean 2-Card Layout)</h2>
      <p>Click the button below to open the clean ErrorReportDetail modal with no duplicate headers:</p>

      <Button type="primary" onClick={handleOpen}>
        Open Error Report Detail
      </Button>

      <ErrorReportDetail
        open={isOpen}
        errorReport={mockErrorReport}
        onClose={handleClose}
      />

      <div style={{ marginTop: "20px", padding: "16px", background: "#f5f5f5", borderRadius: "8px" }}>
        <h3>Simplified View Features:</h3>
        <ul>
          <li>✅ <strong>General Information Card:</strong> User info, error details, and description in one card</li>
          <li>✅ <strong>Media Content Card:</strong> Images and videos in separate card</li>
          <li>✅ <strong>Fixed Image Layout:</strong> Images now fill full container without gaps</li>
          <li>❌ Admin Actions (Removed)</li>
          <li>❌ Impact Level (Removed)</li>
          <li>❌ Notification Controls (Removed)</li>
        </ul>

        <h4>Layout Structure:</h4>
        <ol>
          <li><strong>Error Report Information Card:</strong>
            <ul>
              <li>User: Full Name, Email</li>
              <li>Error: Created Date, URL</li>
              <li>Description: Detailed error description</li>
            </ul>
          </li>
          <li><strong>Media Content Card:</strong>
            <ul>
              <li>Images: 140x140px containers, full fill with object-fit: cover</li>
              <li>Videos: HTML5 player with error handling</li>
              <li>Responsive: 120x120px on mobile</li>
            </ul>
          </li>
        </ol>
      </div>

      <div style={{ marginTop: "20px", padding: "16px", background: "#f0f0f0", borderRadius: "8px" }}>
        <h3>Sample Data Structure:</h3>
        <pre style={{ fontSize: "12px", overflow: "auto", maxHeight: "300px" }}>
          {JSON.stringify(mockErrorReport, null, 2)}
        </pre>
      </div>
    </div>
  );
}

export default ErrorReportDetailDemo;
