import React, { useState } from "react";
import { But<PERSON> } from "antd";
import ErrorReportDetail from "./index";

// Demo data for testing
const mockErrorReport = {
  _id: "67890abcdef12345",
  userId: {
    _id: "user123",
    email: "<EMAIL>",
    fullName: "John Doe"
  },
  description: "The application crashes when trying to upload a large file. This happens consistently with files over 10MB. The error occurs after clicking the upload button and waiting for about 30 seconds. The page becomes unresponsive and shows a white screen.",
  impactLevel: "high",
  errorUrl: "https://app.example.com/upload/documents",
  imageIds: [
    {
      _id: "img1",
      imageFileId: "file123",
      name: "error-screenshot-1.png"
    },
    {
      _id: "img2", 
      imageFileId: "file124",
      name: "error-screenshot-2.png"
    }
  ],
  videoId: {
    _id: "video1",
    name: "error-recording.mp4"
  },
  notified: false,
  adminNotes: "Initial investigation shows this might be related to server timeout. Need to check upload limits.",
  createdAt: "2024-01-15T10:30:00Z",
  updatedAt: "2024-01-15T14:45:00Z"
};

function ErrorReportDetailDemo() {
  const [isOpen, setIsOpen] = useState(false);

  const handleOpen = () => setIsOpen(true);
  const handleClose = () => setIsOpen(false);
  
  const handleUpdate = (updatedReport) => {
    console.log("Updated report:", updatedReport);
  };

  return (
    <div style={{ padding: "20px" }}>
      <h2>ErrorReportDetail Component Demo</h2>
      <p>Click the button below to open the ErrorReportDetail modal with sample data:</p>
      
      <Button type="primary" onClick={handleOpen}>
        Open Error Report Detail
      </Button>

      <ErrorReportDetail
        open={isOpen}
        errorReport={mockErrorReport}
        onClose={handleClose}
        onUpdate={handleUpdate}
      />

      <div style={{ marginTop: "20px", padding: "16px", background: "#f5f5f5", borderRadius: "8px" }}>
        <h3>Sample Data Structure:</h3>
        <pre style={{ fontSize: "12px", overflow: "auto" }}>
          {JSON.stringify(mockErrorReport, null, 2)}
        </pre>
      </div>
    </div>
  );
}

export default ErrorReportDetailDemo;
