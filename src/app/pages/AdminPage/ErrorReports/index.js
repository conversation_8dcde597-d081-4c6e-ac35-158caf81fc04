import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { connect } from "react-redux";
import { Card, Col, Form, Input, Row, Select, Tag, Tooltip, DatePicker } from "antd";
import { useTranslation } from "react-i18next";
import { EyeOutlined, SearchOutlined, CheckOutlined, DeleteOutlined } from "@ant-design/icons";
import dayjs from "dayjs";

import Loading from "@src/app/component/Loading";
import TableAdmin from "@src/app/component/TableAdmin";
import AntButton from "@src/app/component/AntButton";

import { toast } from "@src/app/component/ToastProvider";
import { paginationConfig, handleSearchParams, formatDate, handleReplaceUrlSearch, cloneObj, convertObjectToQuery, getColumnSortOrder, orderColumn } from "@src/common/functionCommons";

import { BUTTON, PAGINATION_INIT } from "@constant";
import { handlePagingData } from "@src/common/dataConverter";

import { getAllErrorReports, updateErrorReport, deleteErrorReport } from "@services/ErrorReport";
import ErrorReportDetail from "./ErrorReportDetail";
import { AntForm } from "@src/app/component/AntForm";
import { confirm } from "@component/ConfirmProvider";

import "./ErrorReports.scss";

function ErrorReports() {
  const location = useLocation();
  const [form] = Form.useForm();
  const { t, i18n } = useTranslation();

  const [errorReportsData, setErrorReportsData] = useState(PAGINATION_INIT);
  const [detailModalState, setDetailModalState] = useState({
    open: false,
    errorReport: null,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isShowSelectDate, setShowSelectDate] = useState(false);
  const [minDate, setMinDate] = useState(null);
  const [maxDate, setMaxDate] = useState(null);

  useEffect(() => {
    const { query, paging } = handleSearchParams(location.search);
    handleQueryFromUrl(query);
    getErrorReportsData(query, paging);
  }, [location.search]);

  const handleQueryFromUrl = (query) => {
    const { time, fromDate, toDate } = query;

    const newQuery = {
      ...query,
      ...fromDate ? { fromDate: dayjs(fromDate * 1000) } : {},
      ...toDate ? { toDate: dayjs(toDate * 1000) } : {},
    };

    setShowSelectDate(time === "custom");
    setMinDate(newQuery?.fromDate);
    setMaxDate(newQuery?.toDate);
    form.setFieldsValue(newQuery);
  };

  const getErrorReportsData = async (query, paging) => {
    setIsLoading(true);
    const searchFields = ["description", "errorUrl"];
    const apiResponse = await getAllErrorReports(paging, query, searchFields);
    if (apiResponse) {
      setErrorReportsData(handlePagingData(apiResponse, query));
    }
    setIsLoading(false);
  };

  const handleViewDetail = (errorReport) => {
    setDetailModalState({ open: true, errorReport });
  };

  const handleCloseDetail = () => {
    setDetailModalState({ open: false, errorReport: null });
  };

  const handleMarkAsNotified = async (record) => {
    setIsLoading(true);
    const updateResponse = await updateErrorReport({
      _id: record._id,
      notified: !record.notified
    });

    if (updateResponse) {
      const rows = errorReportsData.rows.map(item =>
        item._id === record._id ? { ...item, notified: !record.notified } : item
      );
      setErrorReportsData({ ...errorReportsData, rows });
      toast.success(t("ERROR_REPORT_UPDATED_SUCCESS"));
    }
    setIsLoading(false);
  };

  const handleDelete = async (id, description) => {
    confirm.delete({
      title: t("DELETE_ERROR_REPORT"),
      content: t("DELETE_ERROR_REPORT_CONFIRM", { description: description.substring(0, 50) + "..." }),
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async () => {
        setIsLoading(true);
        const apiResponse = await deleteErrorReport(id);
        if (apiResponse) {
          toast.success(t("DELETE_ERROR_REPORT_SUCCESS"));
          getErrorReportsData(errorReportsData.query, errorReportsData.paging);
        } else {
          toast.error(t("DELETE_ERROR_REPORT_ERROR"));
          setIsLoading(false);
        }
      },
    });
  };

  const getImpactLevelColor = (level) => {
    switch (level) {
      case "high": return "red";
      case "medium": return "orange";
      case "low": return "green";
      default: return "default";
    }
  };

  const columns = [
    orderColumn(errorReportsData.paging),
    {
      title: t("USER"),
      dataIndex: ["userId", "email"],
      width: 200,
      render: (email, record) => (
        <div>
          <div>{email}</div>
          <small className="text-muted">{record.userId?.fullName}</small>
        </div>
      ),
    },
    {
      title: t("DESCRIPTION"),
      dataIndex: "description",
      width: 300,
      render: (text) => (
        <div className="error-description">
          {text.length > 100 ? text.substring(0, 100) + "..." : text}
        </div>
      ),
    },
    {
      title: t("IMPACT_LEVEL"),
      dataIndex: "impactLevel",
      width: 120,
      align: "center",
      render: (level) => (
        <Tag color={getImpactLevelColor(level)}>
          {t(`IMPACT_${level?.toUpperCase()}`)}
        </Tag>
      ),
    },
    {
      title: t("ERROR_URL"),
      dataIndex: "errorUrl",
      width: 200,
      render: (url) => (
        <a href={url} target="_blank" rel="noopener noreferrer" className="error-url">
          {url?.length > 30 ? url.substring(0, 30) + "..." : url}
        </a>
      ),
    },
    {
      title: t("CREATED_AT"),
      dataIndex: "createdAt",
      key: "createdAt",
      width: 150,
      render: (value) => formatDate(value),
      sortOrder: getColumnSortOrder("createdAt", errorReportsData?.query),
      sorter: true,
    },
    {
      title: t("ACTIONS"),
      align: "center",
      width: 150,
      render: (_, record) => (
        <div className="error-actions">
          <Tooltip title={t("VIEW_DETAILS")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title={t("DELETE_ERROR_REPORT")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record._id, record.description)}
              className="btn-delete"
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  const pagination = paginationConfig(errorReportsData.paging, errorReportsData.query, i18n.language);

  const submitFormFilter = (values) => {
    const { fromDate, toDate } = values;
    const filterValues = {
      ...values,
      ...fromDate ? { fromDate: dayjs(fromDate)?.startOf("day")?.unix() } : {},
      ...toDate ? { toDate: dayjs(toDate)?.endOf("day")?.unix() } : {},
    };

    handleReplaceUrlSearch(1, errorReportsData.paging.pageSize, filterValues);
  };

  const onClearFilter = () => {
    setShowSelectDate(false);
    form.resetFields();
    submitFormFilter({});
  };

  const handleChangeSelectTime = (value) => {
    if (value === "custom") {
      setShowSelectDate(true);
    } else {
      setShowSelectDate(false);
      form.setFieldsValue({ fromDate: null, toDate: null });
    }
  };

  return (
    <Loading active={isLoading} transparent>
      <div className="error-reports-container">
        <Card className="error-reports-info-card">
          <div className="error-reports-info-header">
            <div>
              <h1 className="error-reports-title">{t("ERROR_REPORTS_MANAGEMENT")}</h1>
              <p className="error-reports-description">{t("ERROR_REPORTS_MANAGEMENT_DESCRIPTION")}</p>
            </div>
          </div>
        </Card>

        <Card className="error-reports-search-card">
          <AntForm form={form} layout="horizontal" size={"large"} className="form-filter" onFinish={submitFormFilter}>
            <Row gutter={24}>
              <Col xs={24} md={12} lg={6}>
                <AntForm.Item name="impactLevel" className="search-form-item">
                  <Select placeholder={t("FILTER_BY_IMPACT_LEVEL")} allowClear>
                    <Select.Option value="low">{t("IMPACT_LOW")}</Select.Option>
                    <Select.Option value="medium">{t("IMPACT_MEDIUM")}</Select.Option>
                    <Select.Option value="high">{t("IMPACT_HIGH")}</Select.Option>
                  </Select>
                </AntForm.Item>
              </Col>
              <Col xs={24} md={12} lg={6}>
                <AntForm.Item name="time" className="search-form-item">
                  <Select placeholder={t("SELECT_TIME")} onChange={handleChangeSelectTime} allowClear>
                    <Select.Option value="week">{t("THIS_WEEK")}</Select.Option>
                    <Select.Option value="month">{t("THIS_MONTH")}</Select.Option>
                    <Select.Option value="custom">{t("CUSTOM")}</Select.Option>
                  </Select>
                </AntForm.Item>
              </Col>
              {isShowSelectDate && (
                <>
                  <Col xs={24} md={12} lg={6}>
                    <AntForm.Item
                      name="fromDate"
                      className="search-form-item"
                      rules={[
                        () => ({
                          validator(_, value) {
                            if (!!value) {
                              return Promise.resolve();
                            }
                            return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                          },
                        }),
                      ]}
                    >
                      <DatePicker
                        placeholder={t("SELECT_FROM_DATE")}
                        size="large"
                        format="DD/MM/YYYY"
                        maxDate={maxDate}
                        onChange={setMinDate}
                        style={{ width: "100%" }}
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12} lg={6}>
                    <AntForm.Item
                      name="toDate"
                      className="search-form-item"
                      rules={[
                        () => ({
                          validator(_, value) {
                            if (!!value) {
                              return Promise.resolve();
                            }
                            return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                          },
                        }),
                      ]}
                    >
                      <DatePicker
                        placeholder={t("SELECT_TO_DATE")}
                        size="large"
                        format="DD/MM/YYYY"
                        minDate={minDate}
                        onChange={setMaxDate}
                        style={{ width: "100%" }}
                      />
                    </AntForm.Item>
                  </Col>
                </>
              )}
            </Row>
            <Row justify="end" className="search-buttons-row">
              <Col>
                <div className="search-buttons">
                  <AntButton type={BUTTON.GHOST_WHITE} size="large" onClick={onClearFilter}>
                    {t("CLEAR")}
                  </AntButton>
                  <AntButton type={BUTTON.DEEP_NAVY} size="large" htmlType="submit">
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="error-reports-table-card">
          <TableAdmin
            columns={columns}
            dataSource={errorReportsData.rows}
            scroll={{ x: 1200 }}
            pagination={pagination}
            rowKey="_id"
            className="error-reports-table"
            rowClassName={() => "error-reports-table-row"}
            locale={{ emptyText: t("NO_ERROR_REPORTS_FOUND") }}
          />
        </Card>

        <ErrorReportDetail
          open={detailModalState.open}
          errorReport={detailModalState.errorReport}
          onClose={handleCloseDetail}
          onUpdate={(updatedReport) => {
            const rows = errorReportsData.rows.map(item =>
              item._id === updatedReport._id ? updatedReport : item
            );
            setErrorReportsData({ ...errorReportsData, rows });
          }}
        />
      </div>
    </Loading>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(ErrorReports);
